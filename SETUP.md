# ADHD Productivity Dashboard - Setup Guide

## Quick Start

### 1. Prerequisites
- <PERSON><PERSON> and <PERSON><PERSON> Compose
- Python 3.11+ (for local development)
- Node.js 18+ (for local development)
- Git

### 2. Initial Setup

```bash
# Clone the repository (if not already done)
git clone <your-repo-url>
cd make-work-easy-dashboard

# Copy environment configuration
cp .env.example .env

# Edit .env file with your preferences
nano .env  # or use your preferred editor
```

### 3. Development with Docker (Recommended)

```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 4. Local Development (Alternative)

```bash
# Run the setup script
./start-dev.sh

# Or manually:

# 1. Start database services
docker-compose up -d postgres redis

# 2. Setup backend
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
alembic upgrade head
uvicorn app.main:app --reload

# 3. Setup frontend (in new terminal)
cd frontend
npm install
npm run dev
```

## Access Points

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## Database Management

```bash
# Create new migration
cd backend
alembic revision --autogenerate -m "Description of changes"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

## Testing the Setup

1. **Backend Health Check**:
   ```bash
   curl http://localhost:8000/health
   ```

2. **Create a Test Task**:
   ```bash
   curl -X POST "http://localhost:8000/api/tasks/" \
        -H "Content-Type: application/json" \
        -d '{"title": "Test Task", "description": "Testing the API"}'
   ```

3. **Check for Duplicates**:
   ```bash
   curl "http://localhost:8000/api/tasks/check-duplicates?title=Test%20Task"
   ```

## Troubleshooting

### Common Issues

1. **Port Already in Use**:
   ```bash
   # Check what's using the port
   lsof -i :8000  # or :5173, :5432, :6379
   
   # Kill the process or change ports in docker-compose.yml
   ```

2. **Database Connection Issues**:
   ```bash
   # Check PostgreSQL status
   docker-compose exec postgres pg_isready -U postgres
   
   # Reset database
   docker-compose down -v
   docker-compose up -d postgres
   ```

3. **Permission Issues**:
   ```bash
   # Make scripts executable
   chmod +x start-dev.sh
   
   # Fix file permissions
   sudo chown -R $USER:$USER .
   ```

### Development Tips

1. **Hot Reload**: Both backend and frontend support hot reload in development mode
2. **Database GUI**: Use pgAdmin or DBeaver to connect to PostgreSQL
3. **Redis GUI**: Use RedisInsight to monitor Redis
4. **API Testing**: Use the built-in Swagger UI at `/docs`

## Next Steps

After setup is complete:

1. ✅ Test basic task creation and duplicate detection
2. 🔄 Implement the dashboard UI components
3. 🔄 Add OCR capabilities
4. 🔄 Integrate email management
5. 🔄 Add real-time features

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   (React)       │◄──►│   (FastAPI)     │◄──►│  (PostgreSQL)   │
│   Port: 5173    │    │   Port: 8000    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │     Redis       │
                       │   (Cache/Jobs)  │
                       │   Port: 6379    │
                       └─────────────────┘
```

## Security Notes

- Change default passwords in `.env`
- Use strong secret keys in production
- Enable HTTPS for production deployment
- Regularly update dependencies
- Review and audit access logs
