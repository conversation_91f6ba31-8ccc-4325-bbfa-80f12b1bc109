# Database Configuration
DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/productivity_db

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application Settings
DEBUG=true
APP_NAME=ADHD Productivity Dashboard
VERSION=1.0.0

# CORS Settings
ALLOWED_ORIGINS=["http://localhost:5173", "http://localhost:3000"]

# OCR Settings
TESSERACT_CMD=/usr/bin/tesseract  # Leave empty for auto-detection
OCR_CONFIDENCE_THRESHOLD=0.7

# Email Settings (Optional)
EMAIL_ENABLED=false
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Microsoft Graph API (Optional - for Outlook integration)
MICROSOFT_CLIENT_ID=your-client-id
MICROSOFT_CLIENT_SECRET=your-client-secret
MICROSOFT_TENANT_ID=your-tenant-id

# Task Management
DUPLICATE_DETECTION_THRESHOLD=0.7
MAX_DUPLICATE_SUGGESTIONS=5

# File Upload
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=uploads

# Background Jobs
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Development
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=productivity_db
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
