@tailwind base;
@tailwind components;
@tailwind utilities;

/* ADHD-friendly base styles */
@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-neutral-50 text-neutral-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    /* Reduce motion for users who prefer it */
    @media (prefers-reduced-motion: reduce) {
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }
  }
  
  /* Ensure good contrast and readability */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold text-neutral-900;
    line-height: 1.4;
  }
  
  p {
    line-height: 1.6;
  }
  
  /* Focus styles for accessibility */
  *:focus-visible {
    @apply outline-2 outline-offset-2 outline-primary-500;
  }
}

@layer components {
  /* ADHD-friendly button styles */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }
  
  .btn-secondary {
    @apply bg-neutral-100 text-neutral-900 hover:bg-neutral-200 active:bg-neutral-300;
  }
  
  .btn-ghost {
    @apply hover:bg-neutral-100 active:bg-neutral-200;
  }
  
  /* Card styles with subtle shadows */
  .card {
    @apply bg-white rounded-xl border border-neutral-200 shadow-sm;
  }
  
  .card-hover {
    @apply transition-shadow hover:shadow-md;
  }
  
  /* Input styles */
  .input {
    @apply flex h-10 w-full rounded-lg border border-neutral-300 bg-white px-3 py-2 text-sm placeholder:text-neutral-500 focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  /* Widget container styles */
  .widget {
    @apply card p-4 h-full flex flex-col;
  }
  
  .widget-header {
    @apply flex items-center justify-between mb-4 pb-2 border-b border-neutral-100;
  }
  
  .widget-title {
    @apply text-lg font-semibold text-neutral-900;
  }
  
  .widget-content {
    @apply flex-1 overflow-hidden;
  }
  
  /* Context-specific styles */
  .context-work {
    @apply border-l-4 border-work;
  }
  
  .context-legal {
    @apply border-l-4 border-legal;
  }
  
  .context-real-estate {
    @apply border-l-4 border-real-estate;
  }
  
  .context-personal {
    @apply border-l-4 border-personal;
  }
  
  /* Status indicators */
  .status-pending {
    @apply bg-neutral-100 text-neutral-700;
  }
  
  .status-in-progress {
    @apply bg-primary-100 text-primary-700;
  }
  
  .status-completed {
    @apply bg-secondary-100 text-secondary-700;
  }
  
  .status-cancelled {
    @apply bg-red-100 text-red-700;
  }
}

@layer utilities {
  /* Custom scrollbar for better UX */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f9fafb;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f9fafb;
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }
  
  /* Animation utilities */
  .animate-in {
    animation: fadeIn 0.3s ease-out;
  }
  
  .animate-out {
    animation: fadeOut 0.2s ease-in;
  }
}
