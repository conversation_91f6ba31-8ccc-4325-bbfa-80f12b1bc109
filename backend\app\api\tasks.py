from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime
from ..services.task_service import TaskService
from ..models.task import TaskStatus, TaskPriority, TaskContext
from ..core.database import get_db

router = APIRouter(prefix="/api/tasks", tags=["tasks"])


class TaskCreate(BaseModel):
    title: str
    description: Optional[str] = None
    context: TaskContext = TaskContext.WORK
    priority: TaskPriority = TaskPriority.MEDIUM
    check_duplicates: bool = True


class TaskUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    context: Optional[TaskContext] = None
    priority: Optional[TaskPriority] = None
    status: Optional[TaskStatus] = None


class TaskResponse(BaseModel):
    id: str
    title: str
    description: Optional[str]
    context: TaskContext
    priority: TaskPriority
    status: TaskStatus
    completed: bool
    created_at: datetime
    updated_at: Optional[datetime]
    source_type: Optional[str]

    class Config:
        from_attributes = True


class DuplicateCheck(BaseModel):
    id: str
    title: str
    similarity: float
    context: Optional[str]
    status: Optional[str]


@router.post("/")
async def create_task(
    task_data: TaskCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new task with duplicate detection
    """
    service = TaskService(db)
    result = await service.create_task(
        title=task_data.title,
        description=task_data.description,
        context=task_data.context,
        priority=task_data.priority,
        check_duplicates=task_data.check_duplicates
    )
    return result


@router.get("/", response_model=List[TaskResponse])
async def get_tasks(
    context: Optional[TaskContext] = Query(None),
    status: Optional[TaskStatus] = Query(None),
    limit: int = Query(100, le=1000),
    offset: int = Query(0, ge=0),
    db: AsyncSession = Depends(get_db)
):
    """
    Get tasks with optional filtering
    """
    service = TaskService(db)
    tasks = await service.get_tasks(
        context=context,
        status=status,
        limit=limit,
        offset=offset
    )
    
    return [
        TaskResponse(
            id=str(task.id),
            title=task.title,
            description=task.description,
            context=task.context,
            priority=task.priority,
            status=task.status,
            completed=task.completed,
            created_at=task.created_at,
            updated_at=task.updated_at,
            source_type=task.source_type
        )
        for task in tasks
    ]


@router.get("/check-duplicates")
async def check_duplicates(
    title: str = Query(..., description="Task title to check for duplicates"),
    threshold: float = Query(0.7, ge=0.0, le=1.0),
    db: AsyncSession = Depends(get_db)
):
    """
    Check for potential duplicate tasks
    """
    service = TaskService(db)
    duplicates = await service.check_duplicates(title, threshold=threshold)
    
    return {
        "potential_duplicates": [
            DuplicateCheck(
                id=str(task.id),
                title=task.title,
                similarity=score,
                context=task.context.value if task.context else None,
                status=task.status.value if task.status else None
            )
            for task, score in duplicates
        ]
    }


@router.patch("/{task_id}/status")
async def update_task_status(
    task_id: str,
    status: TaskStatus,
    db: AsyncSession = Depends(get_db)
):
    """
    Update task status
    """
    service = TaskService(db)
    task = await service.update_task_status(task_id, status)
    
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    return {
        "id": str(task.id),
        "status": task.status.value,
        "completed": task.completed,
        "updated_at": task.updated_at.isoformat() if task.updated_at else None
    }


@router.get("/stats")
async def get_task_stats(
    context: Optional[TaskContext] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    Get task statistics for dashboard widgets
    """
    service = TaskService(db)
    
    # Get tasks for each status
    pending_tasks = await service.get_tasks(context=context, status=TaskStatus.PENDING)
    in_progress_tasks = await service.get_tasks(context=context, status=TaskStatus.IN_PROGRESS)
    completed_tasks = await service.get_tasks(context=context, status=TaskStatus.COMPLETED)
    
    return {
        "total_tasks": len(pending_tasks) + len(in_progress_tasks) + len(completed_tasks),
        "pending": len(pending_tasks),
        "in_progress": len(in_progress_tasks),
        "completed": len(completed_tasks),
        "context": context.value if context else "all"
    }
