{"name": "adhd-productivity-dashboard", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-grid-layout": "^1.4.4", "react-beautiful-dnd": "^13.1.1", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "axios": "^1.6.2", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.5", "react-select": "^5.8.0", "react-datepicker": "^4.24.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-grid-layout": "^1.3.5", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-datepicker": "^4.19.4", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0"}}