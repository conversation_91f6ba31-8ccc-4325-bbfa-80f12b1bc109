# backend/app/models/task.py
from sqlalchemy import Column, String, Text, DateTime, Boolean, Float, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
import uuid

Base = declarative_base()

class Task(Base):
    __tablename__ = "tasks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    context = Column(String(50))  # work, legal, real_estate
    priority = Column(String(20), default="medium")
    status = Column(String(20), default="pending")
    due_date = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed = Column(Boolean, default=False)
    
    # For duplicate detection
    title_vector = Column(Text)  # Will store trigram vector
    
    __table_args__ = (
        Index('idx_task_title_trgm', 'title', postgresql_using='gin', 
              postgresql_ops={'title': 'gin_trgm_ops'}),
        Index('idx_task_context', 'context'),
        Index('idx_task_status', 'status'),
    )

# backend/app/services/task_service.py
from typing import List, Optional, Tuple
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession
from ..models.task import Task
import difflib

class TaskService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def check_duplicates(self, title: str, threshold: float = 0.7) -> List[Tuple[Task, float]]:
        """
        Check for potential duplicate tasks using PostgreSQL trigram similarity
        """
        # Use pg_trgm similarity function
        stmt = (
            select(Task, func.similarity(Task.title, title).label('similarity'))
            .where(Task.completed == False)
            .where(func.similarity(Task.title, title) > threshold)
            .order_by(func.similarity(Task.title, title).desc())
            .limit(5)
        )
        
        result = await self.db.execute(stmt)
        duplicates = result.all()
        
        # Also check with Python's difflib for additional matching
        all_tasks_stmt = select(Task).where(Task.completed == False)
        all_tasks_result = await self.db.execute(all_tasks_stmt)
        all_tasks = all_tasks_result.scalars().all()
        
        python_matches = []
        for task in all_tasks:
            ratio = difflib.SequenceMatcher(None, title.lower(), task.title.lower()).ratio()
            if ratio > threshold:
                python_matches.append((task, ratio))
        
        # Combine and deduplicate results
        combined = {task.id: (task, score) for task, score in duplicates}
        for task, score in python_matches:
            if task.id not in combined or score > combined[task.id][1]:
                combined[task.id] = (task, score)
        
        return sorted(combined.values(), key=lambda x: x[1], reverse=True)[:5]
    
    async def create_task(self, title: str, description: str = None, 
                         context: str = None, check_duplicates: bool = True) -> dict:
        """
        Create a new task with optional duplicate checking
        """
        if check_duplicates:
            duplicates = await self.check_duplicates(title)
            if duplicates:
                return {
                    "status": "duplicate_warning",
                    "duplicates": [
                        {"id": str(task.id), "title": task.title, "similarity": score}
                        for task, score in duplicates
                    ],
                    "message": "Similar tasks found. Create anyway?"
                }
        
        new_task = Task(
            title=title,
            description=description,
            context=context
        )
        self.db.add(new_task)
        await self.db.commit()
        await self.db.refresh(new_task)
        
        return {
            "status": "created",
            "task": {
                "id": str(new_task.id),
                "title": new_task.title,
                "description": new_task.description,
                "context": new_task.context,
                "created_at": new_task.created_at.isoformat()
            }
        }

# backend/app/api/tasks.py
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from pydantic import BaseModel
from ..services.task_service import TaskService
from ..core.database import get_db

router = APIRouter(prefix="/api/tasks", tags=["tasks"])

class TaskCreate(BaseModel):
    title: str
    description: Optional[str] = None
    context: Optional[str] = None
    check_duplicates: bool = True

class TaskResponse(BaseModel):
    id: str
    title: str
    description: Optional[str]
    context: Optional[str]
    priority: str
    status: str
    completed: bool
    created_at: str

@router.post("/")
async def create_task(
    task_data: TaskCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new task with duplicate detection
    """
    service = TaskService(db)
    result = await service.create_task(
        title=task_data.title,
        description=task_data.description,
        context=task_data.context,
        check_duplicates=task_data.check_duplicates
    )
    return result

@router.get("/check-duplicates")
async def check_duplicates(
    title: str,
    threshold: float = 0.7,
    db: AsyncSession = Depends(get_db)
):
    """
    Check for potential duplicate tasks
    """
    service = TaskService(db)
    duplicates = await service.check_duplicates(title, threshold)
    
    return {
        "potential_duplicates": [
            {
                "id": str(task.id),
                "title": task.title,
                "similarity": score,
                "context": task.context
            }
            for task, score in duplicates
        ]
    }

# backend/app/core/database.py
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool
import os
from dotenv import load_dotenv

load_dotenv()

DATABASE_URL = os.getenv("DATABASE_URL")

engine = create_async_engine(
    DATABASE_URL,
    echo=True,
    poolclass=NullPool,
)

async_session_maker = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)

async def get_db():
    async with async_session_maker() as session:
        try:
            yield session
        finally:
            await session.close()

# backend/app/main.py (updated)
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .api import tasks

app = FastAPI(title="ADHD Productivity Dashboard")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(tasks.router)

@app.get("/health")
async def health_check():
    return {"status": "ok", "service": "productivity-dashboard"}

@app.get("/")
async def root():
    return {"message": "ADHD Productivity Dashboard API"}

# To run the database migrations, create this Alembic migration:
# backend/alembic/versions/001_create_tasks_table.py
"""
Create tasks table

Revision ID: 001
Create Date: 2024-01-01

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

def upgrade():
    # Enable extensions
    op.execute('CREATE EXTENSION IF NOT EXISTS pg_trgm')
    op.execute('CREATE EXTENSION IF NOT EXISTS pgcrypto')
    
    # Create tasks table
    op.create_table('tasks',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('context', sa.String(length=50), nullable=True),
        sa.Column('priority', sa.String(length=20), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('due_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed', sa.Boolean(), nullable=True),
        sa.Column('title_vector', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes
    op.create_index('idx_task_title_trgm', 'tasks', ['title'], 
                    postgresql_using='gin', 
                    postgresql_ops={'title': 'gin_trgm_ops'})
    op.create_index('idx_task_context', 'tasks', ['context'])
    op.create_index('idx_task_status', 'tasks', ['status'])

def downgrade():
    op.drop_index('idx_task_status', table_name='tasks')
    op.drop_index('idx_task_context', table_name='tasks')
    op.drop_index('idx_task_title_trgm', table_name='tasks')
    op.drop_table('tasks')