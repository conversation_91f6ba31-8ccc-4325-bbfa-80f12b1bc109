#!/usr/bin/env python3
"""
ADHD Productivity Dashboard - Local Development Setup
This script sets up the development environment without Dock<PERSON>
"""

import os
import sys
import subprocess
import platform
import time
from pathlib import Path

def run_command(command, cwd=None, check=True):
    """Run a command and return the result"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd, 
            check=check,
            capture_output=True,
            text=True
        )
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {command}")
        print(f"Error: {e.stderr}")
        return None

def check_python():
    """Check if Python 3.11+ is available"""
    try:
        result = subprocess.run([sys.executable, "--version"], capture_output=True, text=True)
        version = result.stdout.strip()
        print(f"✅ Python: {version}")
        return True
    except:
        print("❌ Python not found")
        return False

def check_node():
    """Check if Node.js is available"""
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        version = result.stdout.strip()
        print(f"✅ Node.js: {version}")
        return True
    except:
        print("❌ Node.js not found. Please install Node.js 18+")
        return False

def setup_backend():
    """Set up the backend environment"""
    print("\n🐍 Setting up backend...")
    
    backend_dir = Path("backend")
    venv_dir = backend_dir / "venv"
    
    # Create virtual environment
    if not venv_dir.exists():
        print("📦 Creating Python virtual environment...")
        result = run_command(f"{sys.executable} -m venv venv", cwd=backend_dir)
        if not result:
            return False
    
    # Determine activation script
    if platform.system() == "Windows":
        activate_script = venv_dir / "Scripts" / "activate.bat"
        pip_path = venv_dir / "Scripts" / "pip"
    else:
        activate_script = venv_dir / "bin" / "activate"
        pip_path = venv_dir / "bin" / "pip"
    
    # Install dependencies
    print("📦 Installing Python dependencies...")
    result = run_command(f"{pip_path} install -r requirements.txt", cwd=backend_dir)
    if not result:
        return False
    
    print("✅ Backend setup complete!")
    return True

def setup_frontend():
    """Set up the frontend environment"""
    print("\n⚛️  Setting up frontend...")
    
    frontend_dir = Path("frontend")
    node_modules = frontend_dir / "node_modules"
    
    if not node_modules.exists():
        print("📦 Installing Node.js dependencies...")
        result = run_command("npm install", cwd=frontend_dir)
        if not result:
            return False
    
    print("✅ Frontend setup complete!")
    return True

def create_env_file():
    """Create .env file if it doesn't exist"""
    if not Path(".env").exists():
        print("📝 Creating .env file...")
        try:
            with open(".env.example", "r") as src, open(".env", "w") as dst:
                content = src.read()
                # Update for local development (SQLite instead of PostgreSQL)
                content = content.replace(
                    "DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/productivity_db",
                    "DATABASE_URL=sqlite+aiosqlite:///./productivity.db"
                )
                content = content.replace(
                    "REDIS_URL=redis://localhost:6379/0",
                    "REDIS_URL=redis://localhost:6379/0"
                )
                dst.write(content)
            print("✅ .env file created with local development settings")
        except Exception as e:
            print(f"❌ Failed to create .env file: {e}")
            return False
    return True

def show_instructions():
    """Show instructions for running the application"""
    print("\n" + "="*60)
    print("🎉 Setup complete! Here's how to run the application:")
    print("="*60)
    
    print("\n📋 To start the backend:")
    if platform.system() == "Windows":
        print("   cd backend")
        print("   venv\\Scripts\\activate")
        print("   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    else:
        print("   cd backend")
        print("   source venv/bin/activate")
        print("   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    
    print("\n📋 To start the frontend (in a new terminal):")
    print("   cd frontend")
    print("   npm run dev")
    
    print("\n🌐 Access points:")
    print("   - Frontend: http://localhost:5173")
    print("   - Backend API: http://localhost:8000")
    print("   - API Docs: http://localhost:8000/docs")
    
    print("\n💡 Tips:")
    print("   - The backend uses SQLite for local development")
    print("   - No database setup required!")
    print("   - Both services support hot reload")
    
    print("\n🔧 Next steps:")
    print("   1. Start both backend and frontend")
    print("   2. Open http://localhost:5173 in your browser")
    print("   3. Test the task creation and duplicate detection")

def main():
    """Main setup function"""
    print("🚀 ADHD Productivity Dashboard - Local Setup")
    print("=" * 50)
    
    # Check prerequisites
    print("\n🔍 Checking prerequisites...")
    if not check_python():
        sys.exit(1)
    
    if not check_node():
        sys.exit(1)
    
    # Create environment file
    if not create_env_file():
        sys.exit(1)
    
    # Setup backend
    if not setup_backend():
        sys.exit(1)
    
    # Setup frontend
    if not setup_frontend():
        sys.exit(1)
    
    # Show instructions
    show_instructions()

if __name__ == "__main__":
    main()
