from sqlalchemy import Column, String, Text, DateTime, Boolean, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..core.database import Base
import uuid


class Note(Base):
    __tablename__ = "notes"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    
    # Relationships
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=True)  # Optional link to task
    
    # OCR-related fields
    source_type = Column(String(50), default="manual")  # manual, ocr, voice, etc.
    source_file_path = Column(String(500))  # Path to original file if from OCR
    ocr_confidence = Column(String(10))  # OCR confidence score if applicable
    
    # Content metadata
    tags = Column(Text)  # JSON array of tags
    is_encrypted = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="notes")
    task = relationship("Task", back_populates="notes")
    
    __table_args__ = (
        Index('idx_note_title_trgm', 'title', postgresql_using='gin', 
              postgresql_ops={'title': 'gin_trgm_ops'}),
        Index('idx_note_content_trgm', 'content', postgresql_using='gin', 
              postgresql_ops={'content': 'gin_trgm_ops'}),
        Index('idx_note_user_id', 'user_id'),
        Index('idx_note_task_id', 'task_id'),
        Index('idx_note_created_at', 'created_at'),
    )

    def __repr__(self):
        return f"<Note(id={self.id}, title='{self.title}', user_id={self.user_id})>"
