"""Initial migration

Revision ID: 10d0c2d1e8a8
Revises: 
Create Date: 2025-06-24 19:24:50.864107

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '10d0c2d1e8a8'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tasks',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('context', sa.Enum('WORK', 'LEGAL', 'REAL_ESTATE', 'PERSONAL', name='taskcontext'), nullable=True),
    sa.Column('priority', sa.Enum('LOW', 'MEDIUM', 'HIGH', 'URGENT', name='taskpriority'), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', name='taskstatus'), nullable=True),
    sa.Column('due_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed', sa.Boolean(), nullable=True),
    sa.Column('title_vector', sa.Text(), nullable=True),
    sa.Column('source_type', sa.String(length=50), nullable=True),
    sa.Column('source_file_path', sa.String(length=500), nullable=True),
    sa.Column('ocr_confidence', sa.String(length=10), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_task_context', 'tasks', ['context'], unique=False)
    op.create_index('idx_task_created_at', 'tasks', ['created_at'], unique=False)
    op.create_index('idx_task_due_date', 'tasks', ['due_date'], unique=False)
    op.create_index('idx_task_priority', 'tasks', ['priority'], unique=False)
    op.create_index('idx_task_status', 'tasks', ['status'], unique=False)
    op.create_index('idx_task_title_trgm', 'tasks', ['title'], unique=False, postgresql_using='gin', postgresql_ops={'title': 'gin_trgm_ops'})
    op.create_table('users',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('username', sa.String(length=100), nullable=False),
    sa.Column('full_name', sa.String(length=255), nullable=True),
    sa.Column('hashed_password', sa.String(length=255), nullable=False),
    sa.Column('duplicate_threshold', sa.String(length=10), nullable=True),
    sa.Column('preferred_contexts', sa.Text(), nullable=True),
    sa.Column('ui_preferences', sa.Text(), nullable=True),
    sa.Column('notification_settings', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_verified', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_table('notes',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('task_id', sa.UUID(), nullable=True),
    sa.Column('source_type', sa.String(length=50), nullable=True),
    sa.Column('source_file_path', sa.String(length=500), nullable=True),
    sa.Column('ocr_confidence', sa.String(length=10), nullable=True),
    sa.Column('tags', sa.Text(), nullable=True),
    sa.Column('is_encrypted', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['task_id'], ['tasks.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_note_content_trgm', 'notes', ['content'], unique=False, postgresql_using='gin', postgresql_ops={'content': 'gin_trgm_ops'})
    op.create_index('idx_note_created_at', 'notes', ['created_at'], unique=False)
    op.create_index('idx_note_task_id', 'notes', ['task_id'], unique=False)
    op.create_index('idx_note_title_trgm', 'notes', ['title'], unique=False, postgresql_using='gin', postgresql_ops={'title': 'gin_trgm_ops'})
    op.create_index('idx_note_user_id', 'notes', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_note_user_id', table_name='notes')
    op.drop_index('idx_note_title_trgm', table_name='notes', postgresql_using='gin', postgresql_ops={'title': 'gin_trgm_ops'})
    op.drop_index('idx_note_task_id', table_name='notes')
    op.drop_index('idx_note_created_at', table_name='notes')
    op.drop_index('idx_note_content_trgm', table_name='notes', postgresql_using='gin', postgresql_ops={'content': 'gin_trgm_ops'})
    op.drop_table('notes')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index('idx_task_title_trgm', table_name='tasks', postgresql_using='gin', postgresql_ops={'title': 'gin_trgm_ops'})
    op.drop_index('idx_task_status', table_name='tasks')
    op.drop_index('idx_task_priority', table_name='tasks')
    op.drop_index('idx_task_due_date', table_name='tasks')
    op.drop_index('idx_task_created_at', table_name='tasks')
    op.drop_index('idx_task_context', table_name='tasks')
    op.drop_table('tasks')
    # ### end Alembic commands ###
