from sqlalchemy import Column, String, Text, DateTime, Boolean, Index, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from ..core.database import Base
import uuid
import enum


class TaskStatus(str, enum.Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TaskPriority(str, enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class TaskContext(str, enum.Enum):
    WORK = "work"
    LEGAL = "legal"
    REAL_ESTATE = "real_estate"
    PERSONAL = "personal"


class Task(Base):
    __tablename__ = "tasks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    context = Column(Enum(TaskContext), default=TaskContext.WORK)
    priority = Column(Enum(TaskPriority), default=TaskPriority.MEDIUM)
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING)
    due_date = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed = Column(Boolean, default=False)
    
    # For duplicate detection
    title_vector = Column(Text)  # Will store trigram vector
    
    # OCR-related fields
    source_type = Column(String(50))  # manual, ocr, email, etc.
    source_file_path = Column(String(500))  # Path to original file if from OCR
    ocr_confidence = Column(String(10))  # OCR confidence score if applicable
    
    __table_args__ = (
        Index('idx_task_title_trgm', 'title', postgresql_using='gin', 
              postgresql_ops={'title': 'gin_trgm_ops'}),
        Index('idx_task_context', 'context'),
        Index('idx_task_status', 'status'),
        Index('idx_task_priority', 'priority'),
        Index('idx_task_created_at', 'created_at'),
        Index('idx_task_due_date', 'due_date'),
    )

    def __repr__(self):
        return f"<Task(id={self.id}, title='{self.title}', status='{self.status}')>"
