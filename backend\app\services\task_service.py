from typing import List, Optional, Tuple, Dict, Any
from sqlalchemy import select, func, and_
from sqlalchemy.ext.asyncio import AsyncSession
from ..models.task import Task, TaskStatus, TaskPriority, TaskContext
from ..core.config import settings
import difflib
import logging

logger = logging.getLogger(__name__)


class TaskService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def check_duplicates(self, title: str, user_id: str = None, threshold: float = None) -> List[Tuple[Task, float]]:
        """
        Check for potential duplicate tasks using PostgreSQL trigram similarity
        """
        if threshold is None:
            threshold = settings.duplicate_detection_threshold
            
        try:
            # Use pg_trgm similarity function
            stmt = (
                select(Task, func.similarity(Task.title, title).label('similarity'))
                .where(Task.status != TaskStatus.CANCELLED)
                .where(func.similarity(Task.title, title) > threshold)
                .order_by(func.similarity(Task.title, title).desc())
                .limit(settings.max_duplicate_suggestions)
            )
            
            result = await self.db.execute(stmt)
            duplicates = result.all()
            
            # Also check with Python's difflib for additional matching
            all_tasks_stmt = select(Task).where(Task.status != TaskStatus.CANCELLED)
            all_tasks_result = await self.db.execute(all_tasks_stmt)
            all_tasks = all_tasks_result.scalars().all()
            
            python_matches = []
            for task in all_tasks:
                ratio = difflib.SequenceMatcher(None, title.lower(), task.title.lower()).ratio()
                if ratio > threshold:
                    python_matches.append((task, ratio))
            
            # Combine and deduplicate results
            combined = {task.id: (task, score) for task, score in duplicates}
            for task, score in python_matches:
                if task.id not in combined or score > combined[task.id][1]:
                    combined[task.id] = (task, score)
            
            return sorted(combined.values(), key=lambda x: x[1], reverse=True)[:settings.max_duplicate_suggestions]
            
        except Exception as e:
            logger.error(f"Error checking duplicates: {e}")
            return []
    
    async def create_task(self, 
                         title: str, 
                         description: str = None, 
                         context: TaskContext = TaskContext.WORK,
                         priority: TaskPriority = TaskPriority.MEDIUM,
                         user_id: str = None,
                         check_duplicates: bool = True,
                         source_type: str = "manual") -> Dict[str, Any]:
        """
        Create a new task with optional duplicate checking
        """
        try:
            if check_duplicates:
                duplicates = await self.check_duplicates(title, user_id)
                if duplicates:
                    return {
                        "status": "duplicate_warning",
                        "duplicates": [
                            {
                                "id": str(task.id), 
                                "title": task.title, 
                                "similarity": score,
                                "context": task.context.value if task.context else None,
                                "status": task.status.value if task.status else None
                            }
                            for task, score in duplicates
                        ],
                        "message": "Similar tasks found. Create anyway?"
                    }
            
            new_task = Task(
                title=title,
                description=description,
                context=context,
                priority=priority,
                source_type=source_type
            )
            
            self.db.add(new_task)
            await self.db.commit()
            await self.db.refresh(new_task)
            
            logger.info(f"Created new task: {new_task.id}")
            
            return {
                "status": "created",
                "task": {
                    "id": str(new_task.id),
                    "title": new_task.title,
                    "description": new_task.description,
                    "context": new_task.context.value if new_task.context else None,
                    "priority": new_task.priority.value if new_task.priority else None,
                    "status": new_task.status.value if new_task.status else None,
                    "created_at": new_task.created_at.isoformat() if new_task.created_at else None
                }
            }
            
        except Exception as e:
            logger.error(f"Error creating task: {e}")
            await self.db.rollback()
            raise
    
    async def get_tasks(self, 
                       user_id: str = None,
                       context: TaskContext = None,
                       status: TaskStatus = None,
                       limit: int = 100,
                       offset: int = 0) -> List[Task]:
        """
        Get tasks with optional filtering
        """
        try:
            stmt = select(Task)
            
            # Add filters
            conditions = []
            if context:
                conditions.append(Task.context == context)
            if status:
                conditions.append(Task.status == status)
                
            if conditions:
                stmt = stmt.where(and_(*conditions))
            
            stmt = stmt.order_by(Task.created_at.desc()).limit(limit).offset(offset)
            
            result = await self.db.execute(stmt)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Error getting tasks: {e}")
            return []
    
    async def update_task_status(self, task_id: str, status: TaskStatus) -> Optional[Task]:
        """
        Update task status
        """
        try:
            stmt = select(Task).where(Task.id == task_id)
            result = await self.db.execute(stmt)
            task = result.scalar_one_or_none()
            
            if not task:
                return None
                
            task.status = status
            if status == TaskStatus.COMPLETED:
                task.completed = True
            else:
                task.completed = False
                
            await self.db.commit()
            await self.db.refresh(task)
            
            logger.info(f"Updated task {task_id} status to {status}")
            return task
            
        except Exception as e:
            logger.error(f"Error updating task status: {e}")
            await self.db.rollback()
            return None
