from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    # Application
    app_name: str = "ADHD Productivity Dashboard"
    debug: bool = False
    version: str = "1.0.0"
    
    # Database
    database_url: str = "postgresql+asyncpg://postgres:password@localhost:5432/productivity_db"
    
    # Redis
    redis_url: str = "redis://localhost:6379/0"
    
    # Security
    secret_key: str = "your-secret-key-change-this-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # CORS
    allowed_origins: list[str] = ["http://localhost:5173", "http://localhost:3000"]
    
    # OCR Settings
    tesseract_cmd: Optional[str] = None  # Will auto-detect if None
    ocr_confidence_threshold: float = 0.7
    
    # Email Settings
    email_enabled: bool = False
    smtp_server: Optional[str] = None
    smtp_port: int = 587
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None
    
    # Microsoft Graph API (for Outlook integration)
    microsoft_client_id: Optional[str] = None
    microsoft_client_secret: Optional[str] = None
    microsoft_tenant_id: Optional[str] = None
    
    # Task Management
    duplicate_detection_threshold: float = 0.7
    max_duplicate_suggestions: int = 5
    
    # File Upload
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    upload_dir: str = "uploads"
    
    # Background Jobs
    celery_broker_url: str = "redis://localhost:6379/1"
    celery_result_backend: str = "redis://localhost:6379/2"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()


# Database configuration
def get_database_url() -> str:
    """Get the database URL for SQLAlchemy"""
    return settings.database_url


def get_redis_url() -> str:
    """Get the Redis URL"""
    return settings.redis_url
