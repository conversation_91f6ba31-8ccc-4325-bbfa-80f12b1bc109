#!/bin/bash

# ADHD Productivity Dashboard - Development Setup Script

echo "🚀 Starting ADHD Productivity Dashboard Development Environment"
echo "=============================================================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. Please review and update the configuration."
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Start the development environment
echo "🐳 Starting Docker containers..."
docker-compose up -d postgres redis

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
until docker-compose exec postgres pg_isready -U postgres > /dev/null 2>&1; do
    sleep 1
done

echo "✅ PostgreSQL is ready!"

# Wait for Redis to be ready
echo "⏳ Waiting for Redis to be ready..."
until docker-compose exec redis redis-cli ping > /dev/null 2>&1; do
    sleep 1
done

echo "✅ Redis is ready!"

# Install backend dependencies if needed
if [ ! -d "backend/venv" ]; then
    echo "📦 Setting up Python virtual environment..."
    cd backend
    python -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
    cd ..
fi

# Install frontend dependencies if needed
if [ ! -d "frontend/node_modules" ]; then
    echo "📦 Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
fi

# Run database migrations
echo "🗄️  Running database migrations..."
cd backend
source venv/bin/activate
alembic upgrade head
cd ..

echo ""
echo "🎉 Development environment is ready!"
echo ""
echo "📋 Next steps:"
echo "   1. Backend API: cd backend && source venv/bin/activate && uvicorn app.main:app --reload"
echo "   2. Frontend: cd frontend && npm run dev"
echo "   3. Open http://localhost:5173 in your browser"
echo ""
echo "📚 Useful commands:"
echo "   - View API docs: http://localhost:8000/docs"
echo "   - Check health: http://localhost:8000/health"
echo "   - Stop services: docker-compose down"
echo ""
echo "🔧 Configuration:"
echo "   - Edit .env file to customize settings"
echo "   - Database: PostgreSQL on port 5432"
echo "   - Redis: Redis on port 6379"
echo "   - Backend: FastAPI on port 8000"
echo "   - Frontend: React on port 5173"
